from flask import Flask, request, jsonify, send_from_directory
import hmac
import hashlib
import json
import logging
from datetime import datetime
import os
import sys, traceback
from typing import Tuple, Dict, Any, Union, Optional
import argparse
from urllib.parse import urlparse, parse_qs
from waitress import serve
import re


def format_crypto_amount(amount, currency=""):
    """
    Format cryptocurrency amount to avoid scientific notation for small values.

    Args:
        amount: The amount to format (float, int, or string)
        currency: The cryptocurrency code (optional)

    Returns:
        str: Properly formatted amount
    """
    try:
        # Convert to float first
        float_amount = float(amount)

        # For very small values (like BTC), use more decimal places
        if float_amount < 0.0001:
            # Format with up to 10 decimal places, removing trailing zeros
            formatted = f"{float_amount:.10f}".rstrip("0").rstrip(".")
        elif float_amount < 0.01:
            # For small values, use 8 decimal places
            formatted = f"{float_amount:.8f}".rstrip("0").rstrip(".")
        elif float_amount < 1:
            # For medium values, use 6 decimal places
            formatted = f"{float_amount:.6f}".rstrip("0").rstrip(".")
        else:
            # For larger values (like USDT), use 2 decimal places
            formatted = f"{float_amount:.2f}"

        # Add currency code if provided
        if currency:
            return f"{formatted} {currency}"
        return formatted

    except (ValueError, TypeError):
        # Return original value if conversion fails
        if currency:
            return f"{amount} {currency}"
        return str(amount)


# Add the project root directory to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import OXA_PAY_API_KEY, PAYMENT_FEE_RATE  # Make sure this import works in your structure

# Import from project modules
from database.operations import (
    get_user_balance,
    update_user_balance,
    add_transaction,
    get_payment_by_track_id,
    update_payment_status,
    update_payment_status_atomic,
)

# Import centralized payment configuration
from payments.payment_config import (
    DEFAULT_HOST,
    DEFAULT_PORT,
    VERSION,
    SERVICE_NAME,
    DEBUG_MODE,
    TESTING_MODE,
)

# Import currency converter for handling non-USDT payments
from payments.currency_converter import (
    process_payment_with_conversion,
    format_conversion_display,
    get_usdt_equivalent_amount,
)

# Configure logging with UTF-8 encoding to handle Unicode characters
import sys

# Create handlers with explicit UTF-8 encoding
file_handler = logging.FileHandler("flask_callback.log", encoding='utf-8')
stream_handler = logging.StreamHandler(sys.stdout)

# Set encoding for stream handler if possible (Python 3.7+)
if hasattr(stream_handler, 'setStream'):
    try:
        # Try to reconfigure stdout with UTF-8 encoding
        if hasattr(sys.stdout, 'reconfigure'):
            sys.stdout.reconfigure(encoding='utf-8', errors='replace')
    except (AttributeError, OSError):
        # Fallback: stream handler will use system default
        pass

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[file_handler, stream_handler],
)
logger = logging.getLogger("flask_callback")


def create_enhanced_payment_confirmation(
    amount_usdt: float,
    track_id: str,
    new_balance: float,
    bonus_result: Optional[Dict[str, Any]] = None,
    conversion_result: Optional[Dict[str, Any]] = None,
    conversion_display: str = ""
) -> str:
    """
    Create an optimized, user-friendly payment confirmation message.

    Args:
        amount_usdt: The USDT amount credited
        track_id: Payment tracking ID
        new_balance: User's new balance
        bonus_result: Bonus calculation result
        conversion_result: Currency conversion result
        conversion_display: Formatted conversion display

    Returns:
        Formatted confirmation message
    """
    # Check for bonus information
    has_bonus = bonus_result and bonus_result.get("success") and bonus_result.get("bonus_amount", 0) > 0

    # Start with celebratory header and payment details
    lines = [
        "🎉 <b>Payment Successfully Processed!</b>",
        "",
        "<b>━━━━━━━━━━━━━━━━━━</b>",
        f"💵 <b>Amount Received:</b> <code>${amount_usdt:.2f}</code> USDT",
        f"🆔 <b>Transaction ID:</b> <code>{track_id}</code>",
    ]

    # Add bonus information if applicable
    if has_bonus:
        # Use precise decimal arithmetic for display calculations
        from decimal import Decimal

        bonus_amount = bonus_result.get("bonus_amount", 0)
        tier_used = bonus_result.get("tier_used", {})

        # Calculate total credited amount precisely
        amount_decimal = Decimal(str(amount_usdt))
        bonus_decimal = Decimal(str(bonus_amount))
        total_credited = float(amount_decimal + bonus_decimal)

        # Add bonus type information
        if tier_used and tier_used.get("bonus_type") == "fixed":
            tier_threshold = tier_used.get("threshold", 0)
            bonus_type_display = f"Fixed Bonus for ${tier_threshold:.0f}+ deposits"
        else:
            # Percentage-based bonus
            bonus_percentage = tier_used.get("bonus_percentage", 0) * 100 if tier_used else 0
            bonus_type_display = f"{bonus_percentage:.1f}% Bonus"

        # Add detailed breakdown section
        lines.extend([
            "",
            "<b>━━━━━━━━━━━━━━━━━━</b>",
            "<b>💎 Total Credited Breakdown</b>",
            "<b>━━━━━━━━━━━━━━━━━━</b>",
            f"💰 <b>Deposit Amount:</b> <code>${amount_usdt:.2f}</code> USDT",
            f"🎁 <b>Bonus Reward:</b> <code>+${bonus_amount:.2f}</code> USDT ({bonus_type_display})",
            f"💎 <b>Total Credited:</b> <code>${total_credited:.2f}</code> USDT",
        ])

    # Add current balance
    lines.extend([
        "",
        f"🏦 <b>New Balance:</b> <code>${new_balance:.2f}</code> USDT"
    ])

    # Add conversion information if applicable
    if conversion_result and conversion_result.get("is_converted", False):
        lines.extend([
            "",
            conversion_display,
        ])

    # Add congratulatory message for bonus
    if has_bonus:
        bonus_amount = bonus_result.get("bonus_amount", 0)
        tier_used = bonus_result.get("tier_used", {})

        if tier_used and tier_used.get("bonus_type") == "fixed":
            lines.extend([
                "",
                f"🎊 <b>Congratulations!</b> You received a <b>${bonus_amount:.2f}</b> bonus reward! 🎁",
            ])
        else:
            bonus_percentage = tier_used.get("bonus_percentage", 0) * 100 if tier_used else 0
            lines.extend([
                "",
                f"🎊 <b>Congratulations!</b> You received a <b>{bonus_percentage:.1f}%</b> bonus! 🎁",
            ])

    # Footer
    lines.extend([
        "",
        "<b>━━━━━━━━━━━━━━━━━━</b>",
        "✨ <i>Thank you for your payment!</i>",
        "💬 <i>Need help? Contact our support team.</i>",
    ])

    return "\n".join(lines)


# Flask application setup
app = Flask(__name__)
app.config.from_mapping(
    DEBUG=DEBUG_MODE,
    TESTING=TESTING_MODE,
    SECRET_KEY=os.environ.get(
        "FLASK_SECRET_KEY", os.urandom(24).hex()
    ),  # Generate a random key if not provided
    MAX_RETRY_ATTEMPTS=3,  # Maximum attempts for retrying failed operations
)

# Response types
JsonResponse = Tuple[Union[Dict[str, Any], str], int]

# Counter for failed callbacks
failed_callbacks = {}


def verify_hmac_signature(data_bytes: bytes, signature: str, secret_key: str) -> bool:
    """
    Verify HMAC signature from request.

    Args:
        data_bytes: Raw request data bytes
        signature: HMAC signature from request header
        secret_key: Secret key used for signature verification

    Returns:
        bool: True if signature is valid, False otherwise
    """
    if not signature or not secret_key:
        return False

    calculated_hmac = hmac.new(
        secret_key.encode(), data_bytes, hashlib.sha512
    ).hexdigest()
    return hmac.compare_digest(calculated_hmac, signature)


def extract_user_id_from_query(request_url: str) -> Optional[int]:
    """
    Extract user_id from the request URL query parameters.

    Args:
        request_url: The full callback URL with query parameters

    Returns:
        int or None: The extracted user_id as integer, or None if not found
    """
    if not request_url:
        return None

    try:
        # Parse the URL and extract query parameters
        parsed_url = urlparse(request_url)
        query_params = parse_qs(parsed_url.query)

        # Try to get user_id parameter
        if "user_id" in query_params:
            user_id = int(query_params["user_id"][0])
            logger.info(f"Successfully extracted user_id {user_id} from URL parameters")
            return user_id
        else:
            logger.warning(f"No user_id found in URL parameters: {parsed_url.query}")
    except (ValueError, TypeError, IndexError) as e:
        logger.error(f"Error extracting user_id from URL: {e}")

    return None


def process_payment(
    data: Dict[str, Any],
    track_id: str,
    status: str,
    received_amount: Optional[str],
    request_url: Optional[str] = None,
) -> JsonResponse:
    """
    Process payment based on payment status.

    Args:
        data: Payment callback data
        track_id: Payment tracking ID
        status: Payment status
        received_amount: Amount received in payment
        request_url: The original callback URL with possible query parameters

    Returns:
        Response: HTTP response tuple (body, status_code)
    """
    # Check if payment exists in database
    payment = get_payment_by_track_id(track_id)
    if not payment:
        logger.warning(f"Received callback for unknown trackId: {track_id}")
        return jsonify({"error": "Unknown payment reference"}), 404

    # Check if payment has already been processed (atomic check)
    current_status = payment.get("status")
    if current_status == "completed":
        logger.info(f"Payment {track_id} already processed, ignoring callback")
        return (
            jsonify({"status": "success", "message": "Payment already processed"}),
            200,
        )

    # For processing payments, we'll use atomic operations to prevent race conditions
    # Store the current status for atomic updates later
    logger.debug(f"Payment {track_id} current status: {current_status}, processing status: {status}")

    # Extract user_id from multiple possible sources
    user_id = payment.get("user_id")
    url_user_id = extract_user_id_from_query(request_url)

    # Try to extract from email if present (<EMAIL> format)
    email = data.get("email")
    if not user_id and email and "user_" in email:
        try:
            email_user_id = int(email.split("user_")[1].split("@")[0])
            user_id = email_user_id
            logger.info(f"Extracted user_id {user_id} from email {email}")
        except (ValueError, IndexError):
            pass

    # Try to extract from description if present
    description = data.get("description")
    if not user_id and description and "user" in description:
        # Match pattern like "Deposit for user 2026762108"
        match = re.search(r"user\s+(\d+)", description)
        if match:
            try:
                desc_user_id = int(match.group(1))
                user_id = desc_user_id
                logger.info(
                    f"Extracted user_id {user_id} from description: {description}"
                )
            except ValueError:
                pass

    # If user_id is missing from payment but available in URL, use the URL one
    if not user_id and url_user_id:
        user_id = url_user_id
        logger.info(
            f"Using user_id {user_id} from URL parameters for payment {track_id}"
        )

    # Still no user_id? That's a problem.
    if not user_id:
        logger.error(f"Payment {track_id} has no associated user_id")
        return jsonify({"error": "Payment has no associated user"}), 400

    # Verify transaction has enough confirmations
    min_confirmations = 1
    confirmations = 0

    if data.get("txs") and len(data["txs"]) > 0:
        confirmations = int(data["txs"][0].get("confirmations", 0))

    if confirmations < min_confirmations:
        logger.warning(
            f"Payment {track_id} has only {confirmations} confirmations, minimum required is {min_confirmations}"
        )
        return (
            jsonify({"status": "pending", "message": "Waiting for more confirmations"}),
            202,
        )

    # Process payment based on status
    if status in ["completed", "confirmed", "success", "paid", "manual_accept"]:
        # Try to use the enhanced version with currency conversion
        try:
            import asyncio
            return asyncio.run(process_completed_payment_with_conversion(
                data, track_id, status, received_amount, user_id
            ))
        except Exception as e:
            logger.warning(f"Enhanced payment processing failed for {track_id}, falling back to legacy: {e}")
            return process_completed_payment_legacy(
                data, track_id, status, received_amount, user_id
            )
    elif status in ["pending", "confirming", "waiting", "new", "paying"]:
        # For these statuses, we just update the status in the database
        # These are all valid intermediate states that should not be marked as failed
        # Use regular update since these are non-final states
        update_payment_status(track_id, status)
        logger.info(f"Updated payment {track_id} status to {status}")
        return jsonify({"status": "success", "message": "Payment status updated"}), 200
    elif status == "expired":
        # Only mark as failed for expired status - use atomic update to prevent race conditions
        updated_payment = update_payment_status_atomic(
            track_id,
            "failed",
            expected_status=current_status  # Only update if status hasn't changed
        )
        if updated_payment:
            logger.warning(f"Payment {track_id} marked as failed with status {status}")
        else:
            logger.info(f"Payment {track_id} status change to failed skipped - may have been processed concurrently")
        return (
            jsonify({"status": "success", "message": "Payment status updated"}),
            200,
        )
    else:
        # For any other unknown status, just update with the actual status
        # This allows for future status types to be handled properly
        update_payment_status(track_id, status)
        logger.warning(f"Payment {track_id} updated with unknown status: {status}")
        return (
            jsonify({"status": "success", "message": "Payment status updated"}),
            200,
        )


def retry_operation(func, *args, max_attempts=3, **kwargs):
    """
    Retry an operation multiple times before giving up.

    Args:
        func: The function to retry
        *args: Arguments to pass to the function
        max_attempts: Maximum number of attempts
        **kwargs: Keyword arguments to pass to the function

    Returns:
        Result of the function call or raises the last exception
    """
    last_error = None
    for attempt in range(1, max_attempts + 1):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            last_error = e
            logger.warning(f"Attempt {attempt} failed: {str(e)}")
            if attempt == max_attempts:
                logger.error(f"All {max_attempts} attempts failed")
                raise last_error

    # This should never happen if max_attempts > 0
    raise ValueError("Invalid max_attempts value")


async def process_completed_payment_with_conversion(
    data: Dict[str, Any],
    track_id: str,
    status: str,
    received_amount: Optional[str],
    user_id: int,
) -> JsonResponse:
    """
    Enhanced payment processing function that uses currency conversion.
    Handles all types of payments with automatic conversion to USDT.
    """
    try:
        # Use the enhanced currency converter to process the payment
        conversion_result = await process_payment_with_conversion(
            data, track_id, "USDT"
        )

        converted_amount_usdt = conversion_result.get("converted_amount", 0.0)
        conversion_errors = conversion_result.get("conversion_errors", [])

        # Log conversion errors if any
        if conversion_errors:
            logger.warning(f"Conversion errors for {track_id}: {conversion_errors}")

        # Apply fee adjustment (configurable fee rate that needs to be added back)
        if converted_amount_usdt > 0:
            fee_rate = PAYMENT_FEE_RATE  # Use configurable fee rate
            final_amount_usdt = converted_amount_usdt / (1 - fee_rate)

            logger.info(
                f"Applied fee adjustment for {track_id}: "
                f"{converted_amount_usdt:.2f} -> {final_amount_usdt:.2f} USDT "
                f"(adding back {fee_rate*100}% fee)"
            )

            converted_amount_usdt = final_amount_usdt

        # Check for significant overpayment (more than 10% over requested amount)
        original_payment = get_payment_by_track_id(track_id)
        if original_payment and "amount" in original_payment:
            requested_amount = float(original_payment.get("amount", 0))
            if requested_amount > 0 and converted_amount_usdt > requested_amount * 1.1:
                overpayment_percent = ((converted_amount_usdt / requested_amount) - 1) * 100
                logger.info(
                    f"Significant overpayment detected for {track_id}: "
                    f"Requested ${requested_amount:.2f}, Received ${converted_amount_usdt:.2f} USDT "
                    f"({overpayment_percent:.1f}% over requested amount)"
                )

        # Process the payment with the converted USDT amount
        return await _complete_payment_processing(
            data, track_id, status, converted_amount_usdt, user_id, conversion_result
        )

    except Exception as e:
        logger.error(f"Error in enhanced payment processing for {track_id}: {e}")
        # Fallback to legacy processing
        return process_completed_payment_legacy(data, track_id, status, received_amount, user_id)


async def _complete_payment_processing(
    data: Dict[str, Any],
    track_id: str,
    status: str,
    amount_usdt: float,
    user_id: int,
    conversion_result: dict,
) -> JsonResponse:
    """
    Complete the payment processing with USDT amount and conversion details.
    """
    # Atomically update payment status to prevent race conditions
    # Only update if the payment is not already completed
    updated_payment = update_payment_status_atomic(
        track_id,
        "completed",
        expected_status=None,  # Allow any non-completed status
        actual_paid_amount=amount_usdt,
        payment_verified=True,
        conversion_data=conversion_result
    )

    # If the atomic update failed, the payment may have been processed by another callback
    if not updated_payment:
        logger.warning(f"Failed to atomically update payment {track_id} - may have been processed concurrently")
        return jsonify({"status": "success", "message": "Payment already processed"}), 200

    # Double-check that we actually updated to completed status
    if updated_payment.get("status") != "completed":
        logger.error(f"Payment {track_id} status update failed - expected 'completed', got '{updated_payment.get('status')}'")
        return jsonify({"error": "Payment status update failed"}), 500

    # Get the original payment record to retrieve the order_id if needed
    original_payment = get_payment_by_track_id(track_id)
    order_id = data.get("orderId") or (
        original_payment.get("order_id") if original_payment else None
    )

    # Create enhanced payment data record with conversion information
    pay_data = {
        "track_id": track_id,
        "payment_status": status,
        "verification_time": datetime.now().isoformat(),
        "currency": "USDT",  # Always USDT for internal storage
        "callback_received": True,
        "module_name": data.get("module_name"),
        "fee_paid_by_payer": data.get("fee_paid_by_payer"),
        "under_paid_coverage": data.get("under_paid_coverage"),
        "conversion_info": conversion_result,
        "usdt_equivalent": amount_usdt,
        "response_data": {
            "status": data.get("status"),
            "amount": data.get("amount", ""),
            "currency": data.get("currency", ""),
            "orderId": order_id,
            "type": data.get("type"),
            "email": data.get("email"),
            "description": data.get("description"),
        },
        "transactions": data.get("txs", []),
        "blockchain_data": {
            "tx_hash": (
                data.get("txs", [{}])[0].get("tx_hash") if data.get("txs") else None
            ),
            "network": (
                data.get("txs", [{}])[0].get("network") if data.get("txs") else None
            ),
            "confirmations": (
                data.get("txs", [{}])[0].get("confirmations")
                if data.get("txs")
                else None
            ),
            "sender_address": (
                data.get("txs", [{}])[0].get("sender_address")
                if data.get("txs")
                else None
            ),
            "rate": data.get("txs", [{}])[0].get("rate") if data.get("txs") else None,
        },
    }

    # Update user balance if amount is valid
    if amount_usdt > 0:
        try:
            # Get current balance with retry mechanism
            current_balance = retry_operation(
                get_user_balance,
                user_id,
                max_attempts=app.config.get("MAX_RETRY_ATTEMPTS", 3),
            )

            new_balance = current_balance + amount_usdt

            # Update balance with retry mechanism
            retry_operation(
                update_user_balance,
                user_id,
                new_balance,
                max_attempts=app.config.get("MAX_RETRY_ATTEMPTS", 3),
            )

            # Add transaction record with payment data
            try:
                transaction = add_transaction(
                    user_id,
                    "deposit",
                    amount_usdt,  # Store USDT equivalent
                    track_id=track_id,
                    pay_data=pay_data,
                )
                logger.info(f"Enhanced callback transaction created: {transaction}")
            except Exception as tx_err:
                logger.error(f"Failed to create transaction in enhanced callback: {tx_err}")

            # Apply bonus for deposit (if applicable)
            bonus_result = None
            try:
                logger.info(f"Attempting to apply bonus for Flask payment {track_id}, amount: ${amount_usdt:.2f}")
                from utils.bonus_calculator import BonusCalculator

                bonus_result = BonusCalculator.apply_bonus_to_wallet(
                    user_id=user_id,
                    deposit_amount=amount_usdt,
                    track_id=track_id
                )
                logger.info(f"Bonus application result: {bonus_result}")

                if bonus_result.get("success") and bonus_result.get("bonus_amount", 0) > 0:
                    # Update new_balance to include bonus
                    bonus_amount = bonus_result.get("bonus_amount", 0)
                    new_balance = bonus_result.get("new_balance", new_balance)
                    logger.info(
                        f"Applied deposit bonus to user {user_id}: +${bonus_amount:.2f} "
                        f"for Flask payment {track_id}"
                    )
                else:
                    logger.debug(f"No bonus applied for Flask payment {track_id} (amount: ${amount_usdt:.2f})")

            except Exception as bonus_e:
                logger.error(f"Error applying deposit bonus for Flask payment {track_id}: {bonus_e}")
                # Don't fail the payment if bonus application fails
                bonus_result = None

            # Log conversion details
            conversion_details = conversion_result.get("conversion_details", [])
            if conversion_details:
                original_payments = []
                for detail in conversion_details:
                    if detail.get("method") in ["auto_converted", "manual_converted"]:
                        original_payments.append(
                            f"{detail['original_amount']:.8f} {detail['original_currency']}"
                        )
                if original_payments:
                    logger.info(
                        f"Payment {track_id} processed with conversions: "
                        f"Original: {', '.join(original_payments)} → {amount_usdt:.2f} USDT"
                    )

            # Send notification to user (if available)
            try:
                from main import bot
                from utils.telegram_helpers import sync_send_message
                from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

                # Generate conversion display for user notification with fee rate
                conversion_display = format_conversion_display(conversion_result, PAYMENT_FEE_RATE)

                # Create enhanced user notification message
                message_text = create_enhanced_payment_confirmation(
                    amount_usdt=amount_usdt,
                    track_id=track_id,
                    new_balance=new_balance,
                    bonus_result=bonus_result,
                    conversion_result=conversion_result,
                    conversion_display=conversion_display
                )

                markup = InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="💰 View Balance", callback_data="view_balance"
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="🏠 Main Menu", callback_data="return_to_main"
                            )
                        ],
                    ]
                )

                sync_send_message(
                    chat_id=user_id,
                    text=message_text,
                    reply_markup=markup,
                    parse_mode="HTML",
                    bot=bot,
                )
                logger.info(f"Payment confirmation sent to user {user_id} for {track_id}")

            except Exception as notification_err:
                logger.error(f"Failed to send payment notification: {notification_err}")

            logger.info(
                f"Enhanced payment {track_id} processed successfully: "
                f"${amount_usdt:.2f} USDT added to user {user_id}, new balance: ${new_balance:.2f}"
            )

            return jsonify({"status": "success", "message": "Payment processed successfully"}), 200

        except Exception as e:
            logger.error(f"Error processing enhanced payment {track_id}: {e}")
            return jsonify({"error": "Failed to process payment"}), 500
    else:
        logger.warning(f"Payment amount is zero or negative: ${amount_usdt:.2f} USDT")
        return jsonify({"error": "Invalid payment amount"}), 400


def process_completed_payment_legacy(
    data: Dict[str, Any],
    track_id: str,
    status: str,
    received_amount: Optional[str],
    user_id: int,
) -> JsonResponse:
    """
    Legacy payment processing function - kept for backward compatibility.
    """
    # Check for txs data and use accurate received amount
    amount = 0.0

    # Try to get amount from transaction details first (most accurate)
    if data.get("txs") and len(data["txs"]) > 0:
        # Initialize variables for tracking payment details
        total_amount = 0.0
        tx_count = len(data["txs"])
        currency = data["txs"][0].get(
            "currency", "USDT"
        )  # Use currency from first transaction
        is_auto_converted = False
        original_currency = None
        auto_convert_currencies = set()
        original_currencies = set()

        # First pass: collect information about all transactions
        for tx in data["txs"]:
            tx_currency = tx.get("currency", "USDT")
            original_currencies.add(tx_currency)

            # Check if this transaction is auto-converted
            if (
                "auto_convert_amount" in tx
                and tx.get("auto_convert_amount") is not None
            ):
                is_auto_converted = True
                auto_convert_currencies.add(tx.get("auto_convert_currency", "USDT"))

        # Set the currency based on auto-conversion status
        if is_auto_converted and len(auto_convert_currencies) == 1:
            currency = list(auto_convert_currencies)[0]  # Usually USDT
        elif not is_auto_converted and len(original_currencies) == 1:
            currency = list(original_currencies)[0]

        # Log information about multiple currencies if present
        if len(original_currencies) > 1:
            logger.info(
                f"Multiple currencies detected in payment {track_id}: {', '.join(original_currencies)}"
            )

        for tx in data["txs"]:
            tx_currency = tx.get("currency", "USDT")

            # Check for auto-conversion first (non-USDT payments)
            if (
                "auto_convert_amount" in tx
                and tx.get("auto_convert_amount") is not None
            ):
                # This is a payment in another cryptocurrency that was auto-converted to USDT
                try:
                    tx_amount = float(tx.get("auto_convert_amount", 0))
                    auto_convert_currency = tx.get("auto_convert_currency", "USDT")

                    # Special handling for USDT auto-conversions where auto_convert_amount might be 0
                    # but the original transaction is already in USDT
                    if tx_amount == 0 and tx_currency.upper() == "USDT":
                        # For USDT "auto-conversions", use the sent_amount instead
                        tx_amount = float(tx.get("sent_amount", 0))
                        logger.info(
                            f"USDT auto-conversion fix for track_id {track_id}: "
                            f"Using sent_amount {tx_amount} USDT instead of auto_convert_amount 0.0"
                        )

                    # Format amounts properly to avoid scientific notation
                    sent_amount_formatted = format_crypto_amount(
                        tx.get("sent_amount", 0)
                    )
                    tx_amount_formatted = format_crypto_amount(tx_amount)
                    logger.info(
                        f"Found auto-converted payment: {sent_amount_formatted} {tx_currency} -> "
                        f"{tx_amount_formatted} {auto_convert_currency} for track_id {track_id}"
                    )

                    # Set flags for transaction record
                    is_auto_converted = True
                    original_currency = tx_currency
                    currency = (
                        auto_convert_currency  # Use the converted currency (USDT)
                    )

                    # Add to total amount
                    total_amount += tx_amount
                except (ValueError, TypeError) as e:
                    logger.error(f"Error parsing auto_convert_amount: {e}")
                    # Fallback to value field if auto_convert_amount parsing fails
                    try:
                        tx_amount = float(tx.get("value", 0))
                        total_amount += tx_amount
                    except (ValueError, TypeError) as e2:
                        logger.error(f"Error parsing value as fallback: {e2}")
            else:
                # Direct payment - check multiple possible amount fields
                try:
                    # Check for amount fields in order of preference
                    tx_amount = 0.0
                    amount_fields = ["amount", "value", "received_amount", "sent_amount"]
                    for field in amount_fields:
                        if field in tx and tx.get(field) is not None:
                            try:
                                tx_amount = float(tx.get(field, 0))
                                if tx_amount > 0:
                                    break
                            except (ValueError, TypeError):
                                continue

                    # Format amount properly to avoid scientific notation
                    tx_amount_formatted = format_crypto_amount(tx_amount)
                    logger.info(
                        f"Direct payment in {tx_currency}: {tx_amount_formatted} for track_id {track_id}"
                    )

                    # Add to total amount
                    total_amount += tx_amount
                except (ValueError, TypeError) as e:
                    logger.error(f"Error parsing transaction amount fields: {e}")
                    # All amount fields have been checked in the loop above

            # Log transaction details with properly formatted amounts
            sent_amount_formatted = format_crypto_amount(tx.get("sent_amount", "?"))
            tx_amount_formatted = format_crypto_amount(tx_amount)
            logger.info(
                f"Transaction {tx.get('tx_hash', 'unknown')}: "
                f"{sent_amount_formatted} {tx.get('currency', '?')} -> "
                f"{tx_amount_formatted} {currency}"
            )

        # Apply the 1.5% fee adjustment to the total amount
        fee_rate = 0.015  # 1.5%
        total_amount_with_fee = total_amount / (1 - fee_rate)

        # Format amounts properly to avoid scientific notation
        total_formatted = format_crypto_amount(total_amount)
        total_with_fee_formatted = format_crypto_amount(total_amount_with_fee)
        logger.info(
            f"Applied fee adjustment: {total_formatted} -> {total_with_fee_formatted} "
            f"(adding back 1.5% fee) for {track_id}"
        )

        # Use the total amount from all transactions
        if total_amount_with_fee > 0:
            amount = total_amount_with_fee

            # Log detailed information about the payment
            if is_auto_converted:
                # Format amount properly to avoid scientific notation
                amount_formatted = format_crypto_amount(amount)
                logger.info(
                    f"Auto-converted payment from {original_currency} to {currency}. "
                    f"Total from {tx_count} transactions: {amount_formatted} {currency} (after fee adjustment)"
                )
            else:
                # Format amount properly to avoid scientific notation
                amount_formatted = format_crypto_amount(amount)
                logger.info(
                    f"Total from {tx_count} transactions: {amount_formatted} {currency} (after fee adjustment)"
                )

            # Check for significant overpayment (more than 10% over requested amount)
            original_payment = get_payment_by_track_id(track_id)
            if original_payment and "amount" in original_payment:
                requested_amount = float(original_payment.get("amount", 0))
                if requested_amount > 0 and amount > requested_amount * 1.1:
                    # Format amounts properly to avoid scientific notation
                    requested_formatted = format_crypto_amount(requested_amount)
                    amount_formatted = format_crypto_amount(amount)
                    overpayment_percent = ((amount / requested_amount) - 1) * 100
                    logger.info(
                        f"Significant overpayment detected for {track_id}: "
                        f"Requested ${requested_formatted}, Received ${amount_formatted} "
                        f"({overpayment_percent:.1f}% over requested amount)"
                    )
        else:
            logger.warning(
                f"Total transaction amount is zero or negative: {total_amount}"
            )

    # If amount is still 0, fall back to other fields
    if amount <= 0:
        # Try receivedAmount parameter
        if received_amount:
            try:
                parsed_amount = float(received_amount)
                if parsed_amount > 0:
                    amount = parsed_amount
                    logger.info(f"Using received_amount parameter: {amount}")
            except (ValueError, TypeError) as e:
                logger.error(
                    f"Error parsing received_amount: {e}, value: '{received_amount}'"
                )

        # Last resort: try the main amount field
        if amount <= 0 and data.get("amount"):
            try:
                parsed_amount = float(data.get("amount", 0))
                if parsed_amount > 0:
                    amount = parsed_amount
                    logger.info(f"Using main amount field: {amount}")
            except (ValueError, TypeError) as e:
                logger.error(
                    f"Error parsing main amount field: {e}, value: '{data.get('amount')}'"
                )

    # Log a warning if we still have a zero amount
    if amount <= 0:
        logger.warning(
            f"⚠️ Payment amount is still zero or negative after all extraction attempts for track_id {track_id}. "
            f"Original data: receivedAmount={received_amount}, data['amount']={data.get('amount')}, "
            f"txs data: {data.get('txs', [])}"
        )
    else:
        # Format amount properly to avoid scientific notation
        amount_formatted = format_crypto_amount(amount)
        logger.info(
            f"Final determined payment amount for {track_id}: ${amount_formatted}"
        )

    # Update payment status in the database with the actual paid amount
    update_payment_status(
        track_id, "completed", actual_paid_amount=amount, payment_verified=True
    )

    # Get the original payment record to retrieve the order_id if needed
    original_payment = get_payment_by_track_id(track_id)

    # Get the order_id from the callback data or from the original payment record
    order_id = data.get("orderId") or (
        original_payment.get("order_id") if original_payment else None
    )

    # Log the order_id source for debugging
    if data.get("orderId"):
        logger.info(f"Using orderId from callback data: {data.get('orderId')}")
    elif original_payment and original_payment.get("order_id"):
        logger.info(
            f"Using order_id from original payment record: {original_payment.get('order_id')}"
        )
    else:
        logger.warning(
            f"No order_id found in callback data or original payment record for track_id: {track_id}"
        )

    # Extract auto-conversion information
    is_auto_converted = False
    original_currency = None
    auto_convert_amount = None

    if data.get("txs") and len(data["txs"]) > 0:
        tx = data["txs"][0]  # Use the first transaction for simplicity
        if "auto_convert_amount" in tx and tx.get("auto_convert_amount") is not None:
            is_auto_converted = True
            original_currency = tx.get("currency", "Unknown")
            auto_convert_amount = tx.get("auto_convert_amount")
            # Format amounts properly to avoid scientific notation
            sent_amount_formatted = format_crypto_amount(tx.get("sent_amount"))
            convert_amount_formatted = format_crypto_amount(auto_convert_amount)
            logger.info(
                f"Payment {track_id} was auto-converted from {original_currency} to USDT. "
                f"Original amount: {sent_amount_formatted}, Converted amount: {convert_amount_formatted}"
            )

    # Create payment data record
    pay_data = {
        "track_id": track_id,
        "payment_status": status,
        "verification_time": datetime.now().isoformat(),
        "currency": (
            currency if "currency" in locals() else data.get("currency", "USDT")
        ),
        "callback_received": True,
        "module_name": data.get("module_name"),
        "fee_paid_by_payer": data.get("fee_paid_by_payer"),
        "under_paid_coverage": data.get("under_paid_coverage"),
        "is_auto_converted": is_auto_converted,
        "original_currency": original_currency,
        "auto_convert_amount": auto_convert_amount,
        "response_data": {
            "status": data.get("status"),
            "amount": data.get("amount", ""),
            "receivedAmount": received_amount,
            "currency": data.get("currency", ""),
            "orderId": order_id,
            "type": data.get("type"),
            "email": data.get("email"),
            "description": data.get("description"),
        },
        "transactions": data.get("txs", []),
        "blockchain_data": {
            "tx_hash": (
                data.get("txs", [{}])[0].get("tx_hash") if data.get("txs") else None
            ),
            "network": (
                data.get("txs", [{}])[0].get("network") if data.get("txs") else None
            ),
            "confirmations": (
                data.get("txs", [{}])[0].get("confirmations")
                if data.get("txs")
                else None
            ),
            "sender_address": (
                data.get("txs", [{}])[0].get("sender_address")
                if data.get("txs")
                else None
            ),
            "rate": data.get("txs", [{}])[0].get("rate") if data.get("txs") else None,
            "auto_convert_amount": (
                data.get("txs", [{}])[0].get("auto_convert_amount")
                if data.get("txs")
                else None
            ),
            "auto_convert_currency": (
                data.get("txs", [{}])[0].get("auto_convert_currency")
                if data.get("txs")
                else None
            ),
        },
    }

    # Update user balance if amount is valid
    if amount > 0:
        try:
            # Get current balance with retry mechanism
            current_balance = retry_operation(
                get_user_balance,
                user_id,
                max_attempts=app.config.get("MAX_RETRY_ATTEMPTS", 3),
            )

            new_balance = current_balance + amount

            # Update balance with retry mechanism
            retry_operation(
                update_user_balance,
                user_id,
                new_balance,
                max_attempts=app.config.get("MAX_RETRY_ATTEMPTS", 3),
            )

            # Add transaction record with payment data
            try:
                transaction = add_transaction(
                    user_id,
                    "deposit",
                    amount,
                    track_id=track_id,
                    pay_data=pay_data,
                )
                logger.info(f"Callback transaction created: {transaction}")
            except Exception as tx_err:
                logger.error(f"Failed to create transaction in callback: {tx_err}")
                # Track this error but don't fail the whole callback

            # Send message to user about successful payment
            try:
                # Import required modules once
                try:
                    from main import bot
                    from utils.telegram_helpers import sync_send_message
                    from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
                except ImportError as import_err:
                    logger.error(
                        f"Failed to import notification dependencies: {import_err}"
                    )
                    raise

                # Extract transaction hash and other blockchain data for user notification
                tx_hash = None
                network = None
                if data.get("txs") and len(data["txs"]) > 0:
                    tx_hash = data["txs"][0].get("tx_hash")
                    network = data["txs"][0].get("network")

                # Format amounts properly to avoid scientific notation
                amount_formatted = format_crypto_amount(amount)
                balance_formatted = format_crypto_amount(new_balance)

                # Format the payment success message with blockchain details
                message = (
                    f"✅ <b>\u2022 PAYMENT SUCCESSFUL \u2022</b>\n\n"
                    f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
                    f"<b>TRANSACTION COMPLETE</b>\n"
                    f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                    f"<b>Track ID:</b> <code>{track_id}</code>\n"
                    f"<b>Amount Added:</b> <code>${amount_formatted}</code>\n"
                    f"<b>New Balance:</b> <code>${balance_formatted}</code>\n"
                )

                # Add auto-conversion details if applicable
                if is_auto_converted:
                    # Handle multiple currencies if present
                    if len(original_currencies) > 1:
                        # Multiple currencies were used in this payment
                        currency_details = []
                        for tx in data["txs"]:
                            if (
                                "auto_convert_amount" in tx
                                and tx.get("auto_convert_amount") is not None
                            ):
                                tx_currency = tx.get("currency", "Unknown")
                                # Format amounts properly to avoid scientific notation
                                tx_sent_amount = format_crypto_amount(
                                    tx.get("sent_amount", "?")
                                )
                                tx_convert_amount = format_crypto_amount(
                                    tx.get("auto_convert_amount", "?")
                                )
                                currency_details.append(
                                    f"{tx_sent_amount} {tx_currency} -> {tx_convert_amount} USDT"
                                )

                        if currency_details:
                            message += "<b>Multiple Currency Payment:</b>\n"
                            for detail in currency_details:
                                message += f"<code>• {detail}</code>\n"
                    # Single currency auto-conversion
                    elif original_currency and auto_convert_amount:
                        # Format amounts properly to avoid scientific notation
                        original_amount = format_crypto_amount(
                            data["txs"][0].get("sent_amount", "?")
                        )
                        convert_amount = format_crypto_amount(auto_convert_amount)
                        message += (
                            f"<b>Original Payment:</b> <code>{original_amount} {original_currency}</code>\n"
                            f"<b>Auto-converted to:</b> <code>{convert_amount} USDT</code>\n"
                        )

                # Add blockchain details if available
                if tx_hash:
                    message += (
                        f"<b>Network:</b> <code>{network}</code>\n"
                        f"<b>Transaction:</b> <code>{tx_hash[:10]}...{tx_hash[-6:]}</code>\n"
                    )

                message += "\n<i>Your deposit was successful! Your balance has been updated and is available for use.</i>"

                # Create keyboard for the message
                keyboard = InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="💰 View Balance", callback_data="view_balance"
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="🏠 Return to Menu", callback_data="return_to_main"
                            )
                        ],
                    ]
                )

                # Send notification to user
                result = sync_send_message(
                    chat_id=user_id,
                    text=message,
                    reply_markup=keyboard,
                    parse_mode="HTML",
                    bot=bot,
                )

                if result:
                    logger.info(f"Payment success notification sent to user {user_id}")
                else:
                    logger.error(
                        f"Could not send payment notification to user {user_id}"
                    )

                # Also send payment log to admin/log channel using sync method
                try:
                    # Import needed utilities
                    from utils.telegram_helpers import sync_send_message
                    from database.operations import get_log_channel
                    from config import OWNER_ID

                    # Format amounts properly to avoid scientific notation
                    requested_formatted = format_crypto_amount(
                        original_payment.get("amount", 0)
                    )
                    amount_formatted = format_crypto_amount(amount)

                    # Format the payment log message
                    message = (
                        f"💸 <b>\u2022 💲 PAYMENT RECEIVED 💲 \u2022</b>\n\n"
                        f"👤 <b>User ID:</b> <code>{user_id}</code>\n"
                        f"💰 <b>Requested Amount:</b> <code>${requested_formatted}</code>\n"
                        f"💵 <b>Paid Amount:</b> <code>${amount_formatted}</code>\n"
                        f"🧾 <b>Invoice ID:</b> <code>{track_id}</code>\n"
                    )

                    # Add auto-conversion details if applicable
                    if is_auto_converted:
                        # Handle multiple currencies if present
                        if len(original_currencies) > 1:
                            # Multiple currencies were used in this payment
                            message += f"💱 <b>Multiple Currency Payment:</b>\n"
                            for tx in data["txs"]:
                                if (
                                    "auto_convert_amount" in tx
                                    and tx.get("auto_convert_amount") is not None
                                ):
                                    tx_currency = tx.get("currency", "Unknown")
                                    # Format amounts properly to avoid scientific notation
                                    tx_sent_amount = format_crypto_amount(
                                        tx.get("sent_amount", "?")
                                    )
                                    tx_convert_amount = format_crypto_amount(
                                        tx.get("auto_convert_amount", "?")
                                    )
                                    message += f"<code>• {tx_sent_amount} {tx_currency} -> {tx_convert_amount} USDT</code>\n"
                        # Single currency auto-conversion
                        elif original_currency and auto_convert_amount:
                            # Format amounts properly to avoid scientific notation
                            original_amount = format_crypto_amount(
                                data["txs"][0].get("sent_amount", "?")
                            )
                            convert_amount = format_crypto_amount(auto_convert_amount)
                            message += (
                                f"💱 <b>Original Payment:</b> <code>{original_amount} {original_currency}</code>\n"
                                f"🔄 <b>Auto-converted to:</b> <code>{convert_amount} USDT</code>\n"
                            )

                        # Add overpayment notification if applicable
                        if original_payment and "amount" in original_payment:
                            requested_amount = float(original_payment.get("amount", 0))
                            if requested_amount > 0 and amount > requested_amount * 1.1:
                                # Format amounts properly for overpayment notification
                                overpayment_percent = (
                                    (amount / requested_amount) - 1
                                ) * 100
                                requested_formatted = format_crypto_amount(
                                    requested_amount
                                )
                                amount_formatted = format_crypto_amount(amount)
                                message += (
                                    f"⚠️ <b>Overpayment:</b> <code>{overpayment_percent:.1f}% above requested amount</code>\n"
                                    f"⚠️ <b>Requested:</b> <code>${requested_formatted}</code> vs <b>Received:</b> <code>${amount_formatted}</code>\n"
                                )

                    # Add timestamp
                    message += f"⏱ <b>Timestamp:</b> <code>{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</code>"

                    # Log to console/file
                    # Format amount properly to avoid scientific notation
                    amount_formatted = format_crypto_amount(amount)
                    logger.info(
                        f"Payment received: User {user_id}, Amount ${amount_formatted}, Invoice {track_id}"
                    )

                    # Send to log channel
                    channel_id = get_log_channel()
                    if channel_id:
                        sync_send_message(
                            chat_id=channel_id, text=message, parse_mode="HTML", bot=bot
                        )
                        logger.debug(f"Log message sent to channel {channel_id}")

                    # Send to owner
                    if OWNER_ID and isinstance(OWNER_ID, int):
                        sync_send_message(
                            chat_id=OWNER_ID, text=message, parse_mode="HTML", bot=bot
                        )
                except Exception as log_err:
                    logger.error(f"Failed to send payment log notification: {log_err}")
                    # Non-critical error, continue with processing
            except Exception as e:
                logger.error(f"Error while sending payment notification: {e}")
                logger.error(traceback.format_exc())

            # Format amount properly to avoid scientific notation
            amount_formatted = format_crypto_amount(amount)
            logger.info(
                f"Successfully processed payment {track_id} for user {user_id} with amount {amount_formatted}"
            )
        except Exception as e:
            logger.error(f"Failed to update balance for payment {track_id}: {str(e)}")

            # Track failed callback
            if track_id not in failed_callbacks:
                failed_callbacks[track_id] = {
                    "attempts": 1,
                    "last_error": str(e),
                    "last_attempt": datetime.now().timestamp(),
                    "user_id": user_id,
                    "amount": amount,
                    "pay_data": pay_data,
                }
            else:
                failed_callbacks[track_id]["attempts"] += 1
                failed_callbacks[track_id]["last_error"] = str(e)
                failed_callbacks[track_id]["last_attempt"] = datetime.now().timestamp()

            # Return a 500 error if balance update failed
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": "Failed to process payment",
                        "retry": True,
                    }
                ),
                500,
            )
    else:
        logger.warning(f"Payment amount is invalid: {amount}")

    return (
        jsonify({"status": "success", "message": "Payment processed successfully"}),
        200,
    )


@app.route("/callback", methods=["POST"])
def handle_callback() -> JsonResponse:
    """
    Handle payment callbacks from payment processor.
    Validates HMAC signature and processes payments.

    Returns:
        Response: HTTP response with appropriate status code
    """
    try:
        # Get raw request data for HMAC verification
        post_data_bytes = request.get_data()
        post_data = request.get_data(as_text=True)

        # Get the full request URL for extracting query parameters
        request_url = request.url

        # Log the full URL to help with debugging
        logger.info(f"Received callback at URL: {request_url}")

        # Parse JSON data
        try:
            data = json.loads(post_data)
        except json.JSONDecodeError:
            logger.error("Invalid JSON in callback request")
            return jsonify({"error": "Invalid JSON format"}), 400

        # Log the received callback data
        logger.info(f"Received callback data: {json.dumps(data, indent=2, ensure_ascii=True)}")

        # Extract user_id directly from data if present (some payment processors include it)
        user_id_from_data = (
            data.get("user_id") or data.get("userId") or data.get("client_id")
        )
        if user_id_from_data:
            logger.info(f"Found user_id in callback data: {user_id_from_data}")
            # Append user_id to URL for later extraction if needed
            if "?" in request_url:
                request_url += f"&user_id={user_id_from_data}"
            else:
                request_url += f"?user_id={user_id_from_data}"

        # Determine which API key to use based on callback type
        if data.get("type") in ["payment", "invoice"]:
            # Make sure OXA_PAY_API_KEY is loaded correctly from config.py
            api_secret_key = OXA_PAY_API_KEY
        elif data.get("type") == "payout":
            # Assuming the same key for payout, adjust if different
            api_secret_key = OXA_PAY_API_KEY
        else:
            logger.error(f"Invalid data type: {data.get('type')}")
            return jsonify({"error": f"Invalid data type: {data.get('type')}"}), 400

        # Validate HMAC signature if provided
        hmac_header = request.headers.get("HMAC")

        # HMAC verification is required in all modes except explicit testing mode
        if app.config.get("TESTING"):
            logger.info("Running in TESTING mode - skipping HMAC verification")
        else:
            # HMAC header is required for all non-testing requests
            if not hmac_header:
                logger.error("Missing HMAC header in payment callback")
                return jsonify({"error": "Missing HMAC signature"}), 401

            # Verify HMAC signature
            if not verify_hmac_signature(post_data_bytes, hmac_header, api_secret_key):
                logger.error("HMAC verification failed")
                return jsonify({"error": "Invalid HMAC signature"}), 401

            logger.debug("HMAC verification successful")

        # Extract key information from callback - support multiple field name formats
        track_id = (
            data.get("trackId")
            or data.get("track_id")
            or data.get("id")
            or data.get("invoice_id")
        )
        status = data.get("status", "").lower()
        received_amount = (
            data.get("receivedAmount")
            or data.get("received_amount")
            or data.get("amount")
        )

        if not track_id:
            logger.error("Missing trackId/track_id in callback data")
            return jsonify({"error": "Missing track ID"}), 400

        # Process the payment with the full request URL
        return process_payment(data, track_id, status, received_amount, request_url)

    except Exception as e:
        logger.exception(f"Unexpected error in callback handler: {e}")
        return jsonify({"error": "Internal server error", "details": str(e)}), 500


@app.route("/health", methods=["GET"])
def health_check() -> str:
    """
    Simple health check endpoint.

    Returns:
        str: JSON response with server status
    """
    return jsonify(
        {
            "status": "ok",
            "timestamp": datetime.now().timestamp(),
            "service": SERVICE_NAME,
            "version": VERSION,
            "failed_callbacks": len(failed_callbacks),
        }
    )


@app.route("/failed-callbacks", methods=["GET"])
def list_failed_callbacks() -> str:
    """
    List all failed callbacks for manual processing.

    Returns:
        str: JSON response with failed callbacks
    """
    # Only allow in debug or testing mode for security
    if not app.config.get("DEBUG") and not app.config.get("TESTING"):
        return jsonify({"error": "Endpoint only available in debug/testing mode"}), 403

    return jsonify({"count": len(failed_callbacks), "callbacks": failed_callbacks})


@app.route("/uploads/<path:filename>")
def serve_uploads(filename):
    """
    Serve files from the uploads directory.

    Args:
        filename: Path to the file within the uploads directory

    Returns:
        File response
    """
    # Get the root directory of the project
    root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    uploads_dir = os.path.join(root_dir, "uploads")

    # Import the functions to get file metadata
    from utils.file_metadata import (
        get_original_filename,
        get_mime_type,
        normalize_file_path,
    )

    # Normalize the file path to handle various path formats
    normalized_path = normalize_file_path(filename)
    if not normalized_path:
        logger.warning(f"Could not normalize path: {filename}")
        normalized_path = filename  # Fall back to the original path

    # Try to get the original filename from metadata
    metadata_original_filename = get_original_filename(normalized_path)

    # Try to get the mime type from metadata
    mime_type = get_mime_type(normalized_path)

    # If metadata exists, use it; otherwise fall back to the basename
    if metadata_original_filename:
        original_filename = metadata_original_filename
        logger.info(f"Using original filename from metadata: {original_filename}")
    else:
        # Extract the original filename from the path as fallback
        original_filename = os.path.basename(filename)

        # Try to extract original filename from the unique ID filename
        # Format is now: originalname-uniqueid.ext
        parts = original_filename.split("-", 1)
        if len(parts) >= 2:
            # The first part is likely the original filename
            name_part = parts[0]
            # Check if there's an extension in the second part
            if "." in parts[1]:
                ext_part = parts[1].split(".", 1)[1]
                original_filename = f"{name_part}.{ext_part}"
            else:
                original_filename = name_part

        logger.info(f"Using extracted filename: {original_filename}")

    # Log the request
    logger.info(f"Serving file: {filename} from {uploads_dir} as {original_filename}")

    # Determine if we should use as_attachment based on mime_type
    as_attachment = True
    if mime_type and mime_type.startswith("image/"):
        # For images, we might want to display them inline in the browser
        as_attachment = False
        logger.info(f"Serving image with mime_type: {mime_type}")

    # Use as_attachment=True to force download with the original filename
    # or False to allow inline display for images
    return send_from_directory(
        uploads_dir,
        filename,
        as_attachment=as_attachment,
        download_name=original_filename,
        mimetype=mime_type,
    )


def parse_args() -> argparse.Namespace:
    """
    Parse command line arguments.

    Returns:
        Namespace: Parsed arguments
    """
    parser = argparse.ArgumentParser(description="Flask payment callback server")
    parser.add_argument(
        "--host",
        default=DEFAULT_HOST,
        help=f"Host to bind to (default: {DEFAULT_HOST})",
    )
    parser.add_argument(
        "--port",
        type=int,
        default=DEFAULT_PORT,
        help=f"Port to bind to (default: {DEFAULT_PORT})",
    )

    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    parser.add_argument(
        "--testing",
        action="store_true",
        help="Enable testing mode (skip HMAC verification)",
    )
    parser.add_argument(
        "--max-retries",
        type=int,
        default=3,
        help="Maximum retry attempts for database operations",
    )
    return parser.parse_args()


def main() -> None:
    """Main entry point for the application."""
    args = parse_args()

    # Update Flask config based on command-line arguments
    # These override defaults from payment_config.py if provided
    app.config["DEBUG"] = args.debug or DEBUG_MODE
    app.config["TESTING"] = args.testing or TESTING_MODE
    app.config["MAX_RETRY_ATTEMPTS"] = args.max_retries

    if app.config["TESTING"]:
        logger.info("Running in TESTING mode - HMAC verification might be skipped")
    if app.config["DEBUG"]:
        logger.info("Running in DEBUG mode")

    host = args.host
    port = args.port

    print(f"╔══════════════════════════════════════════════════════════════════╗")
    print(f"║ Starting Flask payment callback server on http://{host}:{port}      ║")
    print(f"║ API endpoints:                                                   ║")
    print(f"║ - POST /callback      -> Process payment callbacks                ║")
    print(f"║ - GET  /health        -> Check server status                      ║")
    print(f"║ - GET  /failed-callbacks -> View failed callback attempts         ║")
    print(f"║ - GET  /uploads/<path> -> Serve files from uploads directory      ║")
    print(f"╚══════════════════════════════════════════════════════════════════╝")

    # Add signal handlers for graceful shutdown
    try:
        import signal

        def handle_shutdown(sig, frame):  # frame is required by signal handler API
            logger.info(f"Received shutdown signal {sig}, exiting gracefully...")
            sys.exit(0)

        signal.signal(signal.SIGINT, handle_shutdown)
        signal.signal(signal.SIGTERM, handle_shutdown)
        logger.info("Registered signal handlers for graceful shutdown")
    except Exception as e:
        logger.warning(f"Could not set up signal handlers: {e}")

    # Use waitress for production serving with proper timeout settings
    if app.config["DEBUG"]:
        # Use Flask's built-in server for development
        app.run(host=host, port=port, debug=True)
    else:
        # Use waitress for production with timeouts
        logger.info(f"Starting production server with Waitress on {host}:{port}")
        serve(
            app,
            host=host,
            port=port,
            threads=4,
            connection_limit=1000,
            channel_timeout=30,
            # These timeout settings help prevent hanging connections
            url_scheme="http",
        )


if __name__ == "__main__":
    main()
